<?php
/**
 * Test script to analyze case number search patterns
 * Tests current behavior with asterisk patterns and suffixes
 */

require_once 'config/config.php';
require_once 'services/DosarService.php';

// Test cases to analyze
$testCases = [
    '14096/3/2024*',     // Asterisk pattern
    '14096/3/2024',      // Base case number
    '2333/105/2024/a17', // Case with suffix
    '2333/105/2024',     // Base case number
    '1234/2023*',        // Another asterisk pattern
    '1234/2023/a1'       // Another suffix pattern
];

echo "=== CASE NUMBER PATTERN ANALYSIS ===" . PHP_EOL;
echo "Testing current search behavior with various case number patterns" . PHP_EOL;
echo PHP_EOL;

try {
    $dosarService = new DosarService();
    
    foreach ($testCases as $caseNumber) {
        echo "--- Testing case number: '$caseNumber' ---" . PHP_EOL;
        
        // Test 1: Check detectSearchType function
        require_once 'index.php';
        $detectedType = detectSearchType($caseNumber);
        echo "Detected search type: $detectedType" . PHP_EOL;
        
        // Test 2: Test direct SOAP API call
        echo "Testing SOAP API call..." . PHP_EOL;
        
        $searchParams = [
            'numarDosar' => $caseNumber,
            'institutie' => null,
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        try {
            $results = $dosarService->cautareAvansata($searchParams);
            echo "SOAP API results: " . count($results) . " cases found" . PHP_EOL;
            
            if (!empty($results)) {
                echo "Sample results:" . PHP_EOL;
                foreach (array_slice($results, 0, 3) as $i => $result) {
                    echo "  " . ($i + 1) . ". " . ($result->institutie ?? 'Unknown') . " - " . ($result->numar ?? 'Unknown') . PHP_EOL;
                }
            }
        } catch (Exception $e) {
            echo "SOAP API error: " . $e->getMessage() . PHP_EOL;
        }
        
        // Test 3: Analyze the case number pattern
        echo "Pattern analysis:" . PHP_EOL;
        
        // Check if it contains asterisk
        if (strpos($caseNumber, '*') !== false) {
            echo "  - Contains asterisk wildcard" . PHP_EOL;
            $baseNumber = str_replace('*', '', $caseNumber);
            echo "  - Base number: '$baseNumber'" . PHP_EOL;
        }
        
        // Check if it has additional suffix beyond standard pattern
        if (preg_match('/^(\d+\/\d+\/\d+)\/(.+)$/', $caseNumber, $matches)) {
            echo "  - Has suffix: '{$matches[2]}'" . PHP_EOL;
            echo "  - Base pattern: '{$matches[1]}'" . PHP_EOL;
        } elseif (preg_match('/^(\d+\/\d+)\/(.+)$/', $caseNumber, $matches)) {
            echo "  - Has suffix: '{$matches[2]}'" . PHP_EOL;
            echo "  - Base pattern: '{$matches[1]}'" . PHP_EOL;
        }
        
        // Check current regex patterns
        $standardPattern = '/^\d+\/\d+(?:\/\d+)?$/';
        $prefixPattern = '/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i';
        
        echo "  - Matches standard pattern: " . (preg_match($standardPattern, $caseNumber) ? 'YES' : 'NO') . PHP_EOL;
        echo "  - Matches prefix pattern: " . (preg_match($prefixPattern, $caseNumber) ? 'YES' : 'NO') . PHP_EOL;
        
        echo PHP_EOL;
    }
    
    echo "=== ANALYSIS COMPLETE ===" . PHP_EOL;
    echo "Key findings:" . PHP_EOL;
    echo "1. Current regex patterns do not support asterisk wildcards" . PHP_EOL;
    echo "2. Current regex patterns do not support extended suffixes" . PHP_EOL;
    echo "3. SOAP API receives case numbers literally without preprocessing" . PHP_EOL;
    echo "4. Need to implement preprocessing and client-side filtering" . PHP_EOL;
    
} catch (Exception $e) {
    echo "Error during analysis: " . $e->getMessage() . PHP_EOL;
}
?>
