<?php
/**
 * Debug script pentru căutarea wildcard "14096/3/2024*"
 * Analizează de ce nu găsește dosarul "14096/3/2024"
 */

require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "=== DEBUG WILDCARD SEARCH: 14096/3/2024* ===" . PHP_EOL;

try {
    $dosarService = new DosarService();
    
    // Test 1: Căutare directă pentru "14096/3/2024" (fără asterisk)
    echo "--- Test 1: Căutare directă '14096/3/2024' ---" . PHP_EOL;
    $searchParams1 = [
        'numarDosar' => '14096/3/2024',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $results1 = $dosarService->cautareAvansata($searchParams1);
    echo "Rezultate găsite: " . count($results1) . PHP_EOL;
    
    if (!empty($results1)) {
        echo "Dosare găsite:" . PHP_EOL;
        foreach ($results1 as $i => $dosar) {
            echo "  " . ($i + 1) . ". {$dosar->institutie} - {$dosar->numar}" . PHP_EOL;
        }
    }
    
    echo PHP_EOL;
    
    // Test 2: Căutare cu wildcard "14096/3/2024*" - simulăm procesul complet
    echo "--- Test 2: Căutare wildcard '14096/3/2024*' ---" . PHP_EOL;
    
    // Simulăm normalizarea
    $originalSearch = '14096/3/2024*';
    $caseNumberInfo = [
        'normalized' => '14096/3/2024',
        'original' => '14096/3/2024*',
        'hasWildcard' => true,
        'hasSuffix' => false,
        'suffix' => ''
    ];
    
    echo "Căutare originală: '{$originalSearch}'" . PHP_EOL;
    echo "Număr normalizat pentru SOAP API: '{$caseNumberInfo['normalized']}'" . PHP_EOL;
    echo "Are wildcard: " . ($caseNumberInfo['hasWildcard'] ? 'DA' : 'NU') . PHP_EOL;
    
    // Căutare SOAP API cu numărul normalizat
    $searchParams2 = [
        'numarDosar' => $caseNumberInfo['normalized'],
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $results2 = $dosarService->cautareAvansata($searchParams2);
    echo "Rezultate SOAP API: " . count($results2) . " dosare" . PHP_EOL;
    
    if (!empty($results2)) {
        echo "Dosare găsite de SOAP API:" . PHP_EOL;
        foreach ($results2 as $i => $dosar) {
            echo "  " . ($i + 1) . ". {$dosar->institutie} - {$dosar->numar}" . PHP_EOL;
        }
        
        // Test 3: Aplicăm filtrarea wildcard
        echo PHP_EOL . "--- Test 3: Aplicăm filtrarea wildcard ---" . PHP_EOL;
        
        $filteredResults = [];
        $basePattern = str_replace('*', '', $originalSearch);
        echo "Pattern de bază pentru wildcard: '{$basePattern}'" . PHP_EOL;
        
        foreach ($results2 as $dosar) {
            $caseNumber = $dosar->numar ?? '';
            echo "Verificăm dosarul: '{$caseNumber}'" . PHP_EOL;
            
            if (strpos($caseNumber, $basePattern) === 0) {
                echo "  ✅ MATCH: '{$caseNumber}' începe cu '{$basePattern}'" . PHP_EOL;
                $filteredResults[] = $dosar;
            } else {
                echo "  ❌ NO MATCH: '{$caseNumber}' nu începe cu '{$basePattern}'" . PHP_EOL;
            }
        }
        
        echo PHP_EOL . "Rezultate finale după filtrare wildcard: " . count($filteredResults) . PHP_EOL;
        if (!empty($filteredResults)) {
            echo "Dosare finale:" . PHP_EOL;
            foreach ($filteredResults as $i => $dosar) {
                echo "  " . ($i + 1) . ". {$dosar->institutie} - {$dosar->numar}" . PHP_EOL;
            }
        }
    } else {
        echo "❌ PROBLEMA: SOAP API nu returnează rezultate pentru '{$caseNumberInfo['normalized']}'" . PHP_EOL;
    }
    
    echo PHP_EOL;

    // Test 4: Testăm un wildcard care ar trebui să găsească mai multe dosare
    echo "--- Test 4: Testăm wildcard '14096/3/*' pentru dosare cu sufixe ---" . PHP_EOL;

    $originalSearch4 = '14096/3/*';
    $caseNumberInfo4 = [
        'normalized' => '14096/3/',
        'original' => '14096/3/*',
        'hasWildcard' => true,
        'hasSuffix' => false,
        'suffix' => ''
    ];

    // Căutăm cu pattern parțial
    $searchParams4 = [
        'numarDosar' => '14096/3',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];

    $results4 = $dosarService->cautareAvansata($searchParams4);
    echo "Rezultate SOAP API pentru '14096/3': " . count($results4) . " dosare" . PHP_EOL;

    if (!empty($results4)) {
        echo "Dosare găsite:" . PHP_EOL;
        foreach (array_slice($results4, 0, 10) as $i => $dosar) {
            echo "  " . ($i + 1) . ". {$dosar->institutie} - {$dosar->numar}" . PHP_EOL;
        }

        // Aplicăm filtrarea wildcard pentru '14096/3/*'
        $filteredResults4 = [];
        $basePattern4 = '14096/3/';
        echo PHP_EOL . "Aplicăm filtrare wildcard pentru pattern: '{$basePattern4}*'" . PHP_EOL;

        foreach ($results4 as $dosar) {
            $caseNumber = $dosar->numar ?? '';
            if (strpos($caseNumber, $basePattern4) === 0) {
                echo "  ✅ MATCH: '{$caseNumber}' începe cu '{$basePattern4}'" . PHP_EOL;
                $filteredResults4[] = $dosar;
            }
        }

        echo "Rezultate finale după filtrare: " . count($filteredResults4) . PHP_EOL;
    }

    echo PHP_EOL;

    // Test 5: Căutare mai largă pentru a vedea ce dosare există cu pattern similar
    echo "--- Test 5: Căutare largă pentru pattern '14096/3' ---" . PHP_EOL;
    
    $searchParams5 = [
        'numarDosar' => '14096/3',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $results5 = $dosarService->cautareAvansata($searchParams5);
    echo "Rezultate pentru '14096/3': " . count($results5) . PHP_EOL;

    if (!empty($results5)) {
        echo "Dosare găsite cu pattern '14096/3':" . PHP_EOL;
        foreach (array_slice($results5, 0, 10) as $i => $dosar) {
            echo "  " . ($i + 1) . ". {$dosar->institutie} - {$dosar->numar}" . PHP_EOL;
        }
        if (count($results5) > 10) {
            echo "  ... și încă " . (count($results5) - 10) . " dosare" . PHP_EOL;
        }
    }
    
} catch (Exception $e) {
    echo "Eroare: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== DEBUG COMPLET ===" . PHP_EOL;
?>
